{"originHash": "7b5de47cf5b3b40e488a074bdc5be354ef0ed114464cc6e2212a197aa8eb3ed7", "pins": [{"identity": "combine-schedulers", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/combine-schedulers", "state": {"revision": "9fa31f4403da54855f1e2aeaeff478f4f0e40b13", "version": "1.0.2"}}, {"identity": "hdrhistogram-swift", "kind": "remoteSourceControl", "location": "https://github.com/HdrHistogram/hdrhistogram-swift", "state": {"revision": "a69fa24d7b70421870cafa86340ece900489e17e", "version": "0.1.2"}}, {"identity": "package-benchmark", "kind": "remoteSourceControl", "location": "https://github.com/ordo-one/package-benchmark", "state": {"revision": "75e8622faadd4a960ef93fee76893e41e4e8daf2", "version": "1.25.0"}}, {"identity": "package-jemalloc", "kind": "remoteSourceControl", "location": "https://github.com/ordo-one/package-jemalloc", "state": {"revision": "e8a5db026963f5bfeac842d9d3f2cc8cde323b49", "version": "1.0.0"}}, {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser", "state": {"revision": "41982a3656a71c768319979febd796c6fd111d5c", "version": "1.5.0"}}, {"identity": "swift-atomics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-atomics", "state": {"revision": "cd142fd2f64be2100422d658e7411e39489da985", "version": "1.2.0"}}, {"identity": "swift-case-paths", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-case-paths", "state": {"revision": "642e6aab8e03e5f992d9c83e38c5be98cfad5078", "version": "1.5.5"}}, {"identity": "swift-clocks", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-clocks", "state": {"revision": "b9b24b69e2adda099a1fa381cda1eeec272d5b53", "version": "1.0.5"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections", "state": {"revision": "9bf03ff58ce34478e66aaee630e491823326fd06", "version": "1.1.3"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "bb5059bde9022d69ac516803f4f227d8ac967f71", "version": "1.1.0"}}, {"identity": "swift-custom-dump", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-custom-dump", "state": {"revision": "82645ec760917961cfa08c9c0c7104a57a0fa4b1", "version": "1.3.3"}}, {"identity": "swift-dependencies", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-dependencies", "state": {"revision": "fd1fb25b68fdb9756cd61d23dbd9e2614b340085", "version": "1.4.0"}}, {"identity": "swift-identified-collections", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-identified-collections", "state": {"revision": "2f5ab6e091dd032b63dacbda052405756010dc3b", "version": "1.1.0"}}, {"identity": "swift-navigation", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-navigation", "state": {"revision": "e834b3760731160d7d448509ee6a1408c8582a6b", "version": "2.2.0"}}, {"identity": "swift-numerics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-numerics", "state": {"revision": "0a5bc04095a675662cf24757cc0640aa2204253b", "version": "1.0.2"}}, {"identity": "swift-perception", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-perception", "state": {"revision": "bc67aa8e461351c97282c2419153757a446ae1c9", "version": "1.3.5"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-syntax", "state": {"revision": "cb53fa1bd3219b0b23ded7dfdd3b2baff266fd25", "version": "600.0.0"}}, {"identity": "swift-system", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-system", "state": {"revision": "d2ba781702a1d8285419c15ee62fd734a9437ff5", "version": "1.3.2"}}, {"identity": "texttable", "kind": "remoteSourceControl", "location": "https://github.com/ordo-one/TextTable", "state": {"revision": "a27a07300cf4ae322e0079ca0a475c5583dd575f", "version": "0.0.2"}}, {"identity": "xctest-dynamic-overlay", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/xctest-dynamic-overlay", "state": {"revision": "bc2a151366f2cd0e347274544933bc2acb00c9fe", "version": "1.4.0"}}], "version": 3}