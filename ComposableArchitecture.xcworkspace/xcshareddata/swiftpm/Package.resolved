{"originHash": "0479414dc97c4704849540dc64b58b99ce1f1c648e4cb9269d822b21cbc51b7f", "pins": [{"identity": "combine-schedulers", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/combine-schedulers", "state": {"revision": "5928286acce13def418ec36d05a001a9641086f2", "version": "1.0.3"}}, {"identity": "swift-case-paths", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-case-paths", "state": {"revision": "9810c8d6c2914de251e072312f01d3bf80071852", "version": "1.7.1"}}, {"identity": "swift-clocks", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-clocks", "state": {"revision": "cc46202b53476d64e824e0b6612da09d84ffde8e", "version": "1.0.6"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections", "state": {"revision": "c1805596154bb3a265fd91b8ac0c4433b4348fb0", "version": "1.2.0"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "82a4ae7170d98d8538ec77238b7eb8e7199ef2e8", "version": "1.3.1"}}, {"identity": "swift-custom-dump", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-custom-dump", "state": {"revision": "82645ec760917961cfa08c9c0c7104a57a0fa4b1", "version": "1.3.3"}}, {"identity": "swift-dependencies", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-dependencies", "state": {"revision": "4c90d6b2b9bf0911af87b103bb40f41771891596", "version": "1.9.2"}}, {"identity": "swift-docc-plugin", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-docc-plugin", "state": {"revision": "d1691545d53581400b1de9b0472d45eb25c19fed", "version": "1.4.4"}}, {"identity": "swift-docc-symbolkit", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-docc-symbolkit", "state": {"revision": "b45d1f2ed151d057b54504d653e0da5552844e34", "version": "1.0.0"}}, {"identity": "swift-identified-collections", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-identified-collections", "state": {"revision": "322d9ffeeba85c9f7c4984b39422ec7cc3c56597", "version": "1.1.1"}}, {"identity": "swift-macro-testing", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-macro-testing", "state": {"revision": "2de00af725ff4c43c9a90d7893835de312653169", "version": "0.6.3"}}, {"identity": "swift-navigation", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-navigation", "state": {"revision": "ae208d1a5cf33aee1d43734ea780a09ada6e2a21", "version": "2.3.1"}}, {"identity": "swift-perception", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-perception", "state": {"revision": "d924c62a70fca5f43872f286dbd7cef0957f1c01", "version": "1.6.0"}}, {"identity": "swift-sharing", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-sharing", "state": {"revision": "75e846ee3159dc75b3a29bfc24b6ce5a557ddca9", "version": "2.5.2"}}, {"identity": "swift-snapshot-testing", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-snapshot-testing.git", "state": {"revision": "37230a37e83f1b7023be08e1b1a2603fcb1567fb", "version": "1.18.4"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-syntax", "state": {"revision": "f99ae8aa18f0cf0d53481901f88a0991dc3bd4a2", "version": "601.0.1"}}, {"identity": "swift-tagged", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-tagged.git", "state": {"revision": "3907a9438f5b57d317001dc99f3f11b46882272b", "version": "0.10.0"}}, {"identity": "xctest-dynamic-overlay", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/xctest-dynamic-overlay", "state": {"revision": "39de59b2d47f7ef3ca88a039dff3084688fe27f4", "version": "1.5.2"}}], "version": 2}