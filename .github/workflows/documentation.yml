# Build and deploy DocC to GitHub pages. Based off of @kar<PERSON>'s work here:
# https://github.com/karwa/swift-url/blob/main/.github/workflows/docs.yml
name: Documentation

on:
  release:
    types:
      - published
  push:
    branches:
      - main
  workflow_dispatch:

concurrency:
  group: docs-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: macos-15
    steps:
      - name: Select Xcode 16.2
        run: sudo xcode-select -s /Applications/Xcode_16.2.app

      - name: Checkout Package
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Checkout gh-pages Branch
        uses: actions/checkout@v4
        with:
          ref: gh-pages
          path: docs-out

      - name: Build documentation
        run: >
          rm -rf docs-out/.git;
          rm -rf docs-out/main;
          git tag -l --sort=-v:refname | grep -e "\d\+\.\d\+.0" | tail -n +6 | xargs -I {} rm -rf {};

          for tag in $(echo "main"; git tag -l --sort=-v:refname | grep -e "\d\+\.\d\+.0" | head -6);
          do
            if [ -d "docs-out/$tag/data/documentation/composablearchitecture" ]
            then
              echo "✅ Documentation for "$tag" already exists.";
            else
              echo "⏳ Generating documentation for ComposableArchitecture @ "$tag" release.";
              rm -rf "docs-out/$tag";

              git checkout .;
              git checkout "$tag";

              swift package \
                --allow-writing-to-directory docs-out/"$tag" \
                generate-documentation \
                --target ComposableArchitecture \
                --output-path docs-out/"$tag" \
                --transform-for-static-hosting \
                --hosting-base-path /swift-composable-architecture/"$tag" \
                && echo "✅ Documentation generated for ComposableArchitecture @ "$tag" release." \
                || echo "⚠️ Documentation skipped for ComposableArchitecture @ "$tag".";
            fi;
          done

      - name: Fix permissions
        run: 'sudo chown -R $USER docs-out'

      - name: Publish documentation to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@4.1.7
        with:
          branch: gh-pages
          folder: docs-out
          single-commit: true
