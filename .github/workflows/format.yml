name: Format

on:
  push:
    branches:
      - main

concurrency:
  group: format-${{ github.ref }}
  cancel-in-progress: true

jobs:
  swift_format:
    name: swift-format
    runs-on: macos-15
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - name: Select Xcode 16.2
        run: sudo xcode-select -s /Applications/Xcode_16.2.app
      - name: Format
        run: make format
      - uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: Run swift-format
          branch: 'main'
