name: CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - '*'
  workflow_dispatch:

concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

jobs:
  xcodebuild-latest:
    name: xcodebuild (16)
    runs-on: macos-15
    strategy:
      matrix:
        command: [test, '']
        platform: [IOS, MACOS]
        xcode: ['16.2']
    steps:
      - uses: actions/checkout@v4
      - name: Select Xcode ${{ matrix.xcode }}
        run: sudo xcode-select -s /Applications/Xcode_${{ matrix.xcode }}.app
      - name: Update xcbeautify
        run: brew update && brew upgrade xcbeautify
      - name: List available devices
        run: xcrun simctl list devices available
      - name: Cache derived data
        uses: actions/cache@v3
        with:
          path: |
            ~/.derivedData
          key: |
            deriveddata-xcodebuild-${{ matrix.platform }}-${{ matrix.xcode }}-${{ matrix.command }}-${{ hashFiles('**/Sources/**/*.swift', '**/Tests/**/*.swift') }}
          restore-keys: |
            deriveddata-xcodebuild-${{ matrix.platform }}-${{ matrix.xcode }}-${{ matrix.command }}-
      - name: Set IgnoreFileSystemDeviceInodeChanges flag
        run: defaults write com.apple.dt.XCBuild IgnoreFileSystemDeviceInodeChanges -bool YES
      - name: Update mtime for incremental builds
        uses: chetan/git-restore-mtime-action@v2
      - name: Debug
        run: make XCODEBUILD_ARGUMENT="${{ matrix.command }}" CONFIG=Debug PLATFORM="${{ matrix.platform }}" WORKSPACE=.github/package.xcworkspace xcodebuild

  xcodebuild:
    name: xcodebuild (15)
    runs-on: macos-14
    strategy:
      matrix:
        command: [test, '']
        platform:
          - IOS
          - MAC_CATALYST
          - MACOS
          - TVOS
          # - VISIONOS  # Unfortunately, visionOS on CI is too flakey
          - WATCHOS
        xcode: [15.2, 15.4]
        exclude:
          - {xcode: 15.2, command: test}
          - {xcode: 15.4, command: ''}
          - {xcode: 15.2, platform: MAC_CATALYST}
          - {xcode: 15.2, platform: TVOS}
          # - {xcode: 15.2, platform: VISIONOS}
          - {xcode: 15.2, platform: WATCHOS}
    steps:
      - uses: actions/checkout@v4
      - name: Select Xcode ${{ matrix.xcode }}
        run: sudo xcode-select -s /Applications/Xcode_${{ matrix.xcode }}.app
      - name: Update xcbeautify
        run: brew update && brew upgrade xcbeautify
      - name: Install visionOS runtime
        if: matrix.platform == 'visionOS'
        run: |
          sudo xcodebuild -runFirstLaunch
          sudo xcrun simctl list
          sudo xcodebuild -downloadPlatform visionOS
          sudo xcodebuild -runFirstLaunch
      - name: List available devices
        run: xcrun simctl list devices available
      - name: Cache derived data
        uses: actions/cache@v3
        with:
          path: |
            ~/.derivedData
          key: |
            deriveddata-xcodebuild-${{ matrix.platform }}-${{ matrix.xcode }}-${{ matrix.command }}-${{ hashFiles('**/Sources/**/*.swift', '**/Tests/**/*.swift') }}
          restore-keys: |
            deriveddata-xcodebuild-${{ matrix.platform }}-${{ matrix.xcode }}-${{ matrix.command }}-
      - name: Set IgnoreFileSystemDeviceInodeChanges flag
        run: defaults write com.apple.dt.XCBuild IgnoreFileSystemDeviceInodeChanges -bool YES
      - name: Update mtime for incremental builds
        uses: chetan/git-restore-mtime-action@v2
      - name: Debug
        run: make XCODEBUILD_ARGUMENT="${{ matrix.command }}" CONFIG=Debug PLATFORM="${{ matrix.platform }}" WORKSPACE=.github/package.xcworkspace xcodebuild

  library-evolution:
    name: Library (evolution)
    runs-on: macos-14
    steps:
      - uses: actions/checkout@v4
      - name: Select Xcode 15.4
        run: sudo xcode-select -s /Applications/Xcode_15.4.app
      - name: Update xcbeautify
        run: brew update && brew upgrade xcbeautify
      - name: Build for library evolution
        run: make build-for-library-evolution

  examples:
    name: Examples
    runs-on: macos-15
    steps:
      - uses: actions/checkout@v4
      - name: Cache derived data
        uses: actions/cache@v3
        with:
          path: ~/.derivedData
          key: |
            deriveddata-examples-${{ hashFiles('**/Sources/**/*.swift', '**/Tests/**/*.swift', '**/Examples/**/*.swift') }}
          restore-keys: |
            deriveddata-examples-
      - name: Select Xcode 16
        run: sudo xcode-select -s /Applications/Xcode_16.2.app
      - name: Update xcbeautify
        run: brew update && brew upgrade xcbeautify
      - name: Set IgnoreFileSystemDeviceInodeChanges flag
        run: defaults write com.apple.dt.XCBuild IgnoreFileSystemDeviceInodeChanges -bool YES
      - name: Update mtime for incremental builds
        uses: chetan/git-restore-mtime-action@v2
      - name: CaseStudies (SwiftUI)
        run: make DERIVED_DATA_PATH=~/.derivedData SCHEME="CaseStudies (SwiftUI)" xcodebuild-raw
      - name: CaseStudies (UIKit)
        run: make DERIVED_DATA_PATH=~/.derivedData SCHEME="CaseStudies (UIKit)" xcodebuild-raw
      - name: Search
        run: make DERIVED_DATA_PATH=~/.derivedData SCHEME="Search" xcodebuild-raw
      - name: SyncUps
        run: make DERIVED_DATA_PATH=~/.derivedData SCHEME="SyncUps" xcodebuild-raw
      - name: SpeechRecognition
        run: make DERIVED_DATA_PATH=~/.derivedData SCHEME="SpeechRecognition" xcodebuild-raw
      - name: TicTacToe
        run: make DERIVED_DATA_PATH=~/.derivedData SCHEME="TicTacToe" xcodebuild-raw
      - name: Todos
        run: make DERIVED_DATA_PATH=~/.derivedData SCHEME="Todos" xcodebuild-raw
      - name: VoiceMemos
        run: make DERIVED_DATA_PATH=~/.derivedData SCHEME="VoiceMemos" xcodebuild-raw

  check-macro-compatibility:
    name: Check Macro Compatibility
    runs-on: macos-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Run Swift Macro Compatibility Check
        uses: Matejkob/swift-macro-compatibility-check@v1
        with:
          run-tests: false
          major-versions-only: true
